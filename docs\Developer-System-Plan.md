# ProManage Developer System Implementation Plan

## Overview
A comprehensive developer system that allows marking forms as "in development" and hiding them from regular users while providing full access to developers.

---

## 1. Database Schema

### 1.1 New Table: form_development_status
```sql
CREATE TABLE form_development_status (
    form_name VARCHAR(100) PRIMARY KEY,
    is_in_development BOOLEAN DEFAULT false,
    developer_user_id INTEGER REFERENCES users(user_id),
    development_notes TEXT,
    development_priority VARCHAR(20) DEFAULT 'Medium', -- High, Medium, Low
    estimated_completion_date DATE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by INTEGER REFERENCES users(user_id)
);

-- Indexes for performance
CREATE INDEX idx_form_dev_status_in_development ON form_development_status(is_in_development);
CREATE INDEX idx_form_dev_status_developer ON form_development_status(developer_user_id);
```

### 1.2 Users Table Enhancement
```sql
-- Add developer flag to users table
ALTER TABLE users ADD COLUMN is_developer BO<PERSON>EAN DEFAULT false;

-- Create index for developer lookup
CREATE INDEX idx_users_is_developer ON users(is_developer);

-- Update existing admin users to be developers
UPDATE users SET is_developer = true WHERE role = 'Administrator';
```

### 1.3 Audit Table for Development Changes
```sql
CREATE TABLE form_development_audit (
    audit_id SERIAL PRIMARY KEY,
    form_name VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL, -- 'MARKED_DEVELOPMENT', 'MARKED_PRODUCTION', 'NOTES_UPDATED'
    old_status BOOLEAN,
    new_status BOOLEAN,
    old_notes TEXT,
    new_notes TEXT,
    changed_by INTEGER REFERENCES users(user_id),
    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 2. Core Services Implementation

### 2.1 DeveloperService.cs
**Location:** `Modules/Services/DeveloperService.cs`

```csharp
public static class DeveloperService
{
    #region Form Development Status
    
    public static bool IsFormInDevelopment(string formName)
    {
        const string query = @"
            SELECT COALESCE(is_in_development, false) 
            FROM form_development_status 
            WHERE form_name = @formName";
        
        return DatabaseHelper.ExecuteScalar<bool>(query, new { formName });
    }
    
    public static void SetFormDevelopmentStatus(string formName, bool inDevelopment, string notes = "", string priority = "Medium", DateTime? completionDate = null)
    {
        var userId = UserManager.Instance.CurrentUser.UserId;
        
        // Get current status for audit
        var currentStatus = GetFormDevelopmentDetails(formName);
        
        const string upsertQuery = @"
            INSERT INTO form_development_status 
            (form_name, is_in_development, developer_user_id, development_notes, development_priority, estimated_completion_date, last_modified_by)
            VALUES (@formName, @inDevelopment, @userId, @notes, @priority, @completionDate, @userId)
            ON CONFLICT (form_name) 
            DO UPDATE SET 
                is_in_development = @inDevelopment,
                developer_user_id = @userId,
                development_notes = @notes,
                development_priority = @priority,
                estimated_completion_date = @completionDate,
                last_modified = CURRENT_TIMESTAMP,
                last_modified_by = @userId";
        
        DatabaseHelper.ExecuteNonQuery(upsertQuery, new { 
            formName, inDevelopment, userId, notes, priority, completionDate 
        });
        
        // Log audit trail
        LogDevelopmentChange(formName, currentStatus?.IsInDevelopment, inDevelopment, 
                           currentStatus?.DevelopmentNotes, notes, userId);
    }
    
    #endregion
    
    #region Developer User Management
    
    public static bool IsCurrentUserDeveloper()
    {
        var userId = UserManager.Instance.CurrentUser.UserId;
        return IsUserDeveloper(userId);
    }
    
    public static bool IsUserDeveloper(int userId)
    {
        const string query = "SELECT COALESCE(is_developer, false) FROM users WHERE user_id = @userId";
        return DatabaseHelper.ExecuteScalar<bool>(query, new { userId });
    }
    
    public static void SetUserDeveloperStatus(int userId, bool isDeveloper)
    {
        const string query = "UPDATE users SET is_developer = @isDeveloper WHERE user_id = @userId";
        DatabaseHelper.ExecuteNonQuery(query, new { userId, isDeveloper });
    }
    
    #endregion
    
    #region Development Reporting
    
    public static List<FormDevelopmentModel> GetAllDevelopmentForms()
    {
        const string query = @"
            SELECT 
                fds.form_name,
                fds.is_in_development,
                fds.developer_user_id,
                u.full_name as developer_name,
                fds.development_notes,
                fds.development_priority,
                fds.estimated_completion_date,
                fds.created_date,
                fds.last_modified
            FROM form_development_status fds
            LEFT JOIN users u ON u.user_id = fds.developer_user_id
            WHERE fds.is_in_development = true
            ORDER BY fds.development_priority DESC, fds.estimated_completion_date ASC";
        
        return DatabaseHelper.ExecuteQuery<FormDevelopmentModel>(query);
    }
    
    #endregion
}
```

### 2.2 Enhanced PermissionService Integration
**Update:** `Modules/Services/PermissionService.cs`

```csharp
public static List<string> GetVisibleForms(int userId)
{
    var isDeveloper = DeveloperService.IsUserDeveloper(userId);
    
    const string query = @"
        SELECT DISTINCT rp.form_name
        FROM role_permissions rp
        JOIN users u ON u.role_id = rp.role_id
        LEFT JOIN user_permissions up ON up.user_id = u.user_id AND up.form_name = rp.form_name
        LEFT JOIN form_development_status fds ON fds.form_name = rp.form_name
        WHERE u.user_id = @userId
        AND (COALESCE(up.read_permission, rp.read_permission) = true)
        AND (
            fds.is_in_development IS NULL 
            OR fds.is_in_development = false 
            OR @isDeveloper = true
        )
        ORDER BY rp.form_name";
    
    return DatabaseHelper.ExecuteQuery<string>(query, new { userId, isDeveloper });
}
```

---

## 3. Data Models

### 3.1 FormDevelopmentModel.cs
**Location:** `Modules/Models/FormDevelopmentModel.cs`

```csharp
public class FormDevelopmentModel
{
    public string FormName { get; set; }
    public bool IsInDevelopment { get; set; }
    public int? DeveloperUserId { get; set; }
    public string DeveloperName { get; set; }
    public string DevelopmentNotes { get; set; }
    public string DevelopmentPriority { get; set; }
    public DateTime? EstimatedCompletionDate { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastModified { get; set; }
}
```

---

## 4. User Interface Implementation

### 4.1 DeveloperManagementForm.cs
**Location:** `Forms/MainForms/DeveloperManagementForm.cs`

**Features:**
- Grid showing all forms with development status
- Toggle development mode for forms
- Assign developer to forms
- Development notes and priority
- Estimated completion dates
- Development audit trail

**Access Control:**
- Only accessible to users with `is_developer = true`
- Hidden from ribbon menu for non-developers

### 4.2 MainFrame.cs Integration
**Update ribbon filtering logic:**

```csharp
private void FilterRibbonByPermissions()
{
    var userId = UserManager.Instance.CurrentUser.UserId;
    var isDeveloper = DeveloperService.IsCurrentUserDeveloper();
    
    // Show Developer Management only to developers
    btnDeveloperManagement.Visibility = isDeveloper 
        ? BarItemVisibility.Always 
        : BarItemVisibility.Never;
    
    // Filter other forms based on development status
    var visibleForms = PermissionService.GetVisibleForms(userId);
    
    foreach (var formButton in GetAllFormButtons())
    {
        var formName = GetFormNameFromButton(formButton);
        formButton.Visibility = visibleForms.Contains(formName)
            ? BarItemVisibility.Always 
            : BarItemVisibility.Never;
    }
}
```

---

## 5. Implementation Steps

### Step 1: Database Setup (30 minutes)
1. Create `form_development_status` table
2. Add `is_developer` column to users
3. Create audit table
4. Set up indexes

### Step 2: Core Services (1 hour)
1. Create `DeveloperService.cs`
2. Create `FormDevelopmentModel.cs`
3. Update `PermissionService.cs`

### Step 3: UI Implementation (2 hours)
1. Create `DeveloperManagementForm.cs`
2. Update `MainFrame.cs` ribbon filtering
3. Add developer menu options

### Step 4: Integration & Testing (1 hour)
1. Test form visibility logic
2. Test developer toggles
3. Verify audit trail
4. Test permission integration

---

## 6. Usage Workflow

### For Developers:
1. Mark form as "In Development" via Developer Management
2. Form becomes invisible to regular users
3. Continue development with full access
4. Mark as "Production Ready" when complete
5. Form becomes visible to users with permissions

### For Administrators:
1. Assign developer status to users
2. Monitor development progress
3. Review development audit trail
4. Manage development priorities

### For Regular Users:
1. Cannot see forms marked as "In Development"
2. No access to developer management tools
3. Normal permission system applies to production forms

This system provides complete isolation of development work while maintaining the existing permission structure for production forms.
