# ProManage RBAC Implementation - Detailed Answers

## 1. Adding New Roles

### Where to Add New Roles:
**Database Location:** `roles` table
**UI Location:** `Forms/MainForms/RoleMasterForm.cs` (already exists but needs enhancement)

### Implementation:
```sql
-- Add new role to database
INSERT INTO roles (role_name, description, is_active) 
VALUES ('CustomRole', 'Custom role description', true);

-- Automatically create default permissions for new role
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT r.role_id, f.form_name, false, false, false, false, false
FROM roles r
CROSS JOIN (SELECT DISTINCT form_name FROM role_permissions) f
WHERE r.role_name = 'CustomRole';
```

### RoleMasterForm Enhancement:
- Add role creation/editing functionality
- Grid to manage role permissions
- Auto-populate permissions for new roles

---

## 2. Implementation Timeline (Hours, Not Weeks)

### Realistic Timeline: 8-12 Hours Total

**Phase 1 (2-3 hours):** Core Permission Service
- Create `PermissionService.cs`
- Create permission models
- Database connection setup

**Phase 2 (3-4 hours):** Permission Management UI
- Enhance `RoleMasterForm.cs`
- Create `PermissionManagementForm.cs`
- 3-tab interface implementation

**Phase 3 (2-3 hours):** Form Integration
- Update `MainFrame.cs` ribbon filtering
- Add permission checks to existing forms
- Update `UserMasterForm.cs`

**Phase 4 (1-2 hours):** Testing & Polish
- Test permission scenarios
- Bug fixes and refinements

---

## 3. MainForms Folder Focus

### Current MainForms:
```
Forms/MainForms/
├── DatabaseForm.cs
├── ParametersForm.cs
├── RoleMasterForm.cs
├── SQLQueryForm.cs
├── UserManagementListForm.cs
└── UserMasterForm.cs
```

### Permission System Scope:
**ONLY** forms in `Forms/MainForms/` will be included in the permission system.
- Other folders (`EntryForms`, `ChildForms`, `ReusableForms`) are excluded
- This keeps the permission system focused and manageable

---

## 4. Automatic Form Detection System

### Auto-Discovery Script:
**File:** `Modules/Services/FormDiscoveryService.cs`

```csharp
public static class FormDiscoveryService
{
    public static void SyncFormsWithDatabase()
    {
        // 1. Scan MainForms folder for .cs files
        var mainFormsPath = Path.Combine(Application.StartupPath, "Forms", "MainForms");
        var formFiles = Directory.GetFiles(mainFormsPath, "*.cs")
            .Where(f => !f.EndsWith(".Designer.cs"))
            .Select(f => Path.GetFileNameWithoutExtension(f))
            .ToList();

        // 2. Get existing forms from database
        var existingForms = GetFormsFromDatabase();

        // 3. Add new forms to permission system
        var newForms = formFiles.Except(existingForms).ToList();
        foreach (var formName in newForms)
        {
            AddFormToPermissionSystem(formName);
        }

        // 4. Remove deleted forms from permission system
        var deletedForms = existingForms.Except(formFiles).ToList();
        foreach (var formName in deletedForms)
        {
            RemoveFormFromPermissionSystem(formName);
        }
    }
}
```

### Database Auto-Sync:
```sql
-- Add new form to all roles with default permissions (false)
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT role_id, @formName, false, false, false, false, false
FROM roles
WHERE NOT EXISTS (
    SELECT 1 FROM role_permissions 
    WHERE role_id = roles.role_id AND form_name = @formName
);

-- Remove form from permission system
DELETE FROM user_permissions WHERE form_name = @formName;
DELETE FROM role_permissions WHERE form_name = @formName;
```

### Integration Points:
- Run `FormDiscoveryService.SyncFormsWithDatabase()` on application startup
- Add menu option in admin tools to manually sync
- Log all changes for audit trail

---

## 5. Developer System Implementation

### Database Schema Addition:
```sql
-- Add developer flag to forms tracking
CREATE TABLE form_development_status (
    form_name VARCHAR(100) PRIMARY KEY,
    is_in_development BOOLEAN DEFAULT false,
    developer_user_id INTEGER REFERENCES users(user_id),
    development_notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add developer role flag to users
ALTER TABLE users ADD COLUMN is_developer BOOLEAN DEFAULT false;
```

### Developer Service:
**File:** `Modules/Services/DeveloperService.cs`

```csharp
public static class DeveloperService
{
    public static bool IsFormInDevelopment(string formName)
    {
        // Check if form is marked as in development
        return DatabaseHelper.ExecuteScalar<bool>(
            "SELECT is_in_development FROM form_development_status WHERE form_name = @formName",
            new { formName });
    }

    public static bool IsCurrentUserDeveloper()
    {
        var userId = UserManager.Instance.CurrentUser.UserId;
        return DatabaseHelper.ExecuteScalar<bool>(
            "SELECT is_developer FROM users WHERE user_id = @userId",
            new { userId });
    }

    public static void SetFormDevelopmentStatus(string formName, bool inDevelopment, string notes = "")
    {
        DatabaseHelper.ExecuteNonQuery(@"
            INSERT INTO form_development_status (form_name, is_in_development, developer_user_id, development_notes)
            VALUES (@formName, @inDevelopment, @userId, @notes)
            ON CONFLICT (form_name) 
            DO UPDATE SET 
                is_in_development = @inDevelopment,
                developer_user_id = @userId,
                development_notes = @notes,
                last_modified = CURRENT_TIMESTAMP",
            new { formName, inDevelopment, userId = UserManager.Instance.CurrentUser.UserId, notes });
    }
}
```

### Form Visibility Logic:
```csharp
public static List<string> GetVisibleForms(int userId)
{
    var isDeveloper = DeveloperService.IsCurrentUserDeveloper();
    
    var query = @"
        SELECT DISTINCT rp.form_name
        FROM role_permissions rp
        JOIN users u ON u.role_id = rp.role_id
        LEFT JOIN user_permissions up ON up.user_id = u.user_id AND up.form_name = rp.form_name
        LEFT JOIN form_development_status fds ON fds.form_name = rp.form_name
        WHERE u.user_id = @userId
        AND (COALESCE(up.read_permission, rp.read_permission) = true)
        AND (fds.is_in_development IS NULL OR fds.is_in_development = false OR @isDeveloper = true)";
    
    return DatabaseHelper.ExecuteQuery<string>(query, new { userId, isDeveloper });
}
```

### Developer Management UI:
**File:** `Forms/MainForms/DeveloperManagementForm.cs`
- Grid showing all forms with development status
- Toggle development mode for forms
- Assign developer to forms
- Development notes field
- Only accessible to users with `is_developer = true`

This system ensures:
1. Forms in development are hidden from regular users
2. Only developers can see and access development forms
3. Easy toggle between development and production modes
4. Audit trail of development activities
